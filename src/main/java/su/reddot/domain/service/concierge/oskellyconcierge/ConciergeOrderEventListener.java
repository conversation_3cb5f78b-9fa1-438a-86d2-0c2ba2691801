package su.reddot.domain.service.concierge.oskellyconcierge;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.concierge.api.ConciergePurchaseOrderControllerApi;
import su.reddot.domain.service.concierge.exception.ConciergeOrderProcessingException;
import su.reddot.domain.service.concierge.model.OrderInfoDTO;
import su.reddot.domain.service.concierge.model.OfferPairDTO;
import su.reddot.domain.service.concierge.bitrix.OrderPositionBitrixService;
import su.reddot.domain.service.metric.MicrometerService;
import su.reddot.domain.service.notification.OrderHoldEvent;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Слушатель событий заказов для интеграции с консьерж-сервисом.
 * Обрабатывает события удержания заказов и передает их в консьерж-сервис для дальнейшей обработки.
 */
@Slf4j
@Component
public class ConciergeOrderEventListener extends ConciergeHTTPHandlerService {

    private static final String METRIC_PROCESS_ORDER = "ConciergePurchaseOrderControllerApi.processOrder";

    private final ConciergePurchaseOrderControllerApi conciergePurchaseOrderControllerApi;
    private final Executor asyncExecutor;
    private final OrderService orderService;
    private final OrderPositionBitrixService orderPositionBitrixService;

    public ConciergeOrderEventListener(
            MicrometerService micrometerService,
            ConciergePurchaseOrderControllerApi conciergePurchaseOrderControllerApi,
            @Qualifier("taskExecutor") Executor asyncExecutor,
            OrderService orderService,
            OrderPositionBitrixService orderPositionBitrixService) {
        super(micrometerService);
        this.conciergePurchaseOrderControllerApi = conciergePurchaseOrderControllerApi;
        this.asyncExecutor = asyncExecutor;
        this.orderService = orderService;
        this.orderPositionBitrixService = orderPositionBitrixService;
    }

    /**
     * Обрабатывает событие удержания заказа асинхронно, отправляя информацию в консьерж-сервис.
     *
     * @param event событие удержания заказа
     */
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void onOrderHoldEvent(OrderHoldEvent event) {
        if (event == null || event.getOrderId() == null) {
            log.warn("Получено некорректное событие OrderHoldEvent: {}", event);
            return;
        }

        Long orderId = event.getOrderId();
        log.debug("Обработка события OrderHoldEvent для orderId: {}", orderId);

        CompletableFuture.supplyAsync(() -> createOrderInfoDTO(orderId), asyncExecutor)
                .thenApplyAsync(this::processOrder, asyncExecutor)
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        log.warn("Не удалось обработать заказ {} ", orderId, throwable);
                    } else {
                        log.debug("Заказ успешно обработан: {}", orderId);
                    }
                });
    }

    /**
     * Создает DTO с информацией о заказе для передачи в консьерж-сервис.
     *
     * @param orderId идентификатор заказа
     * @return DTO с информацией о заказе
     */
    private OrderInfoDTO createOrderInfoDTO(Long orderId) {
        return new OrderInfoDTO()
                .orderID(orderId)
                .productIds(getProductIdByOrderId(orderId))
                .clientId(getClientIdByOrderId(orderId));
    }

    /**
     * Обрабатывает заказ, выполняя HTTP-вызов и логируя результат.
     *
     * @param orderInfo информация о заказе
     * @return CompletableFuture с результатом обработки
     */
    private CompletableFuture<Void> processOrder(OrderInfoDTO orderInfo) {
        long startTime = System.currentTimeMillis();
        log.info("Начало обработки заказа: orderId={}, clientId={}, products={}, orderDetails={}",
                orderInfo.getOrderID(), orderInfo.getClientId(), orderInfo.getProductIds(), orderInfo);

        return executeOrderProcessing(orderInfo)
                .thenApply(offerPairs -> {
                    processOfferPairs(offerPairs, orderInfo.getOrderID());
                    return null;
                })
                .handle((result, throwable) -> {
                    long duration = System.currentTimeMillis() - startTime;
                    if (throwable != null) {
                        log.error("Ошибка при обработке заказа: orderId={}, clientId={}, productId={}, длительность={}мс, типОшибки={}, сообщениеОбОшибке={}",
                                orderInfo.getOrderID(), orderInfo.getClientId(), orderInfo.getProductIds(),
                                duration, throwable.getClass().getSimpleName(), throwable.getMessage(), throwable);
                        throw new ConciergeOrderProcessingException("Не удалось обработать заказ: " + orderInfo.getOrderID(),
                                throwable, orderInfo.getOrderID(), HttpStatus.BAD_REQUEST, null);
                    }
                    log.info("Успешная обработка заказа: orderId={}, clientId={}, productId={}, длительность={}мс",
                            orderInfo.getOrderID(), orderInfo.getClientId(), orderInfo.getProductIds(), duration);
                    return null;
                });
    }

    /**
     * Выполняет HTTP-вызов для обработки заказа с учетом метрик.
     *
     * @param orderInfo информация о заказе
     * @return CompletableFuture с результатом HTTP-вызова
     */
    private CompletableFuture<List<OfferPairDTO>> executeOrderProcessing(OrderInfoDTO orderInfo) {
        return CompletableFuture.supplyAsync(() -> executeHttpCall(METRIC_PROCESS_ORDER, () -> {
            log.debug("Выполнение HTTP-вызова для заказа orderId={}", orderInfo.getOrderID());
            return conciergePurchaseOrderControllerApi.processOrder(orderInfo);
        }), asyncExecutor);
    }

    /**
     * Получает список ID продуктов по ID заказа.
     *
     * @param orderId идентификатор заказа
     * @return список ID продуктов
     */
    private Set<Long> getProductIdByOrderId(Long orderId) {
        return Optional.ofNullable(getOrderOrLog(orderId))
                .map(Order::getOrderPositions)
                .filter(positions -> !positions.isEmpty())
                .map(positions -> positions.stream()
                        .filter(position -> position.getProductItem() != null && position.getProductItem().getProduct() != null)
                        .map(position -> position.getProductItem().getProduct().getId())
                        .collect(Collectors.toSet()))
                .orElseGet(() -> {
                    log.warn("Нет позиций в заказе для orderId: {}", orderId);
                    return Collections.emptySet();
                });
    }

    /**
     * Получает ID клиента по ID заказа.
     *
     * @param orderId идентификатор заказа
     * @return ID клиента или null, если не найден
     */
    private Long getClientIdByOrderId(Long orderId) {
        return Optional.ofNullable(getOrderOrLog(orderId))
                .map(Order::getBuyer)
                .map(User::getId)
                .orElseGet(() -> {
                    log.warn("Заказ или покупатель не найден для orderId: {}", orderId);
                    return null;
                });
    }

    /**
     * Обрабатывает список пар предложений и создает записи в таблице order_position_bitrix.
     *
     * @param offerPairs список пар предложений
     * @param originalOrderId идентификатор исходного заказа
     */
    private void processOfferPairs(List<OfferPairDTO> offerPairs, Long originalOrderId) {
        if (offerPairs == null || offerPairs.isEmpty()) {
            log.debug("Нет пар предложений для обработки для заказа orderId={}", originalOrderId);
            return;
        }
        log.info("Обработка {} пар предложений для заказа orderId={}", offerPairs.size(), originalOrderId);

        offerPairs.forEach(offerPair -> executeWithErrorHandling(() -> {
                    processOfferPair(offerPair, originalOrderId);
                    return null;
                }, "Ошибка при обработке пары предложений: bitrixId={}, orderId={}, originalOrderId={}",
                offerPair.getBitrixId(), offerPair.getOrderId(), originalOrderId));
    }

    /**
     * Обрабатывает одну пару предложений и создает записи в таблице order_position_bitrix.
     *
     * @param offerPair пара предложений
     * @param originalOrderId идентификатор исходного заказа
     */
    private void processOfferPair(OfferPairDTO offerPair, Long originalOrderId) {
        Long bitrixId = offerPair.getBitrixId();
        Long orderId = offerPair.getOrderId();

        if (bitrixId == null || orderId == null) {
            log.warn("Некорректная пара предложений: bitrixId={}, orderId={}, originalOrderId={}",
                    bitrixId, orderId, originalOrderId);
            return;
        }

        log.debug("Обработка пары предложений: bitrixId={}, orderId={}, originalOrderId={}", bitrixId, orderId, originalOrderId);

        List<OrderPosition> orderPositions = getOrderPositionsByOrderId(orderId);
        if (orderPositions.isEmpty()) {
            log.warn("Не найдены позиции заказа для orderId={}, bitrixId={}", orderId, bitrixId);
            return;
        }

        orderPositions.forEach(orderPosition -> executeWithErrorHandling(() -> {
                    OrderPositionBitrixService.CreateRequest createRequest = new OrderPositionBitrixService.CreateRequest()
                            .setOrderPositionId(orderPosition.getId())
                            .setBitrixDealId(bitrixId);
                    orderPositionBitrixService.createOrderPositionBitrix(createRequest);
                    log.info("Создана запись в order_position_bitrix: orderPositionId={}, bitrixDealId={}",
                            orderPosition.getId(), bitrixId);
                    return null;
                }, "Ошибка при создании записи в order_position_bitrix: orderPositionId={}, bitrixDealId={}",
                orderPosition.getId(), bitrixId));
    }

    /**
     * Получает список позиций заказа по ID заказа.
     *
     * @param orderId идентификатор заказа
     * @return список позиций заказа
     */
    private List<OrderPosition> getOrderPositionsByOrderId(Long orderId) {
        return Optional.ofNullable(getOrderOrLog(orderId))
                .map(Order::getOrderPositions)
                .filter(positions -> !positions.isEmpty())
                .orElseGet(() -> {
                    log.warn("Нет позиций в заказе для orderId: {}", orderId);
                    return Collections.emptyList();
                });
    }

    /**
     * Получает заказ с обработкой ошибок и логированием.
     *
     * @param orderId идентификатор заказа
     * @return заказ или null, если не найден
     */
    private Order getOrderOrLog(Long orderId) {
        return executeWithErrorHandling(() -> {
            Order order = orderService.getOrder(orderId);
            if (order == null) {
                log.warn("Заказ не найден для orderId: {}", orderId);
            }
            return order;
        }, "Ошибка при получении заказа для orderId: {}", orderId);
    }

    /**
     * Выполняет операцию с обработкой ошибок и логированием.
     *
     * @param supplier поставщик результата
     * @param errorMessage шаблон сообщения об ошибке
     * @param args аргументы для сообщения об ошибке
     * @param <T> тип результата
     * @return результат операции или null в случае ошибки
     */
    private <T> T executeWithErrorHandling(Supplier<T> supplier, String errorMessage, Object... args) {
        try {
            return supplier.get();
        } catch (Exception e) {
            log.error(errorMessage, args, e);
            return null;
        }
    }
}