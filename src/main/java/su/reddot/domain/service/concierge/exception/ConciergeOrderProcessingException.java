package su.reddot.domain.service.concierge.exception;

import org.springframework.http.HttpStatus;
import su.reddot.domain.exception.concierge.CustomConciergeException;
import su.reddot.domain.service.concierge.oskellyconcierge.dto.ConciergeErrorResponse;
/**
 * Исключение для обработки ошибок при обработке заказа консьерж-сервисом
 */
public class ConciergeOrderProcessingException extends CustomConciergeException {

    public ConciergeOrderProcessingException(String message, Throwable cause, Long orderId, HttpStatus httpCode, ConciergeErrorResponse conciergeErrorResponse) {
        super(message, cause, orderId, httpCode, conciergeErrorResponse);
    }
}
