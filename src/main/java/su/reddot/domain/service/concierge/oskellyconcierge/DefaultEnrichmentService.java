package su.reddot.domain.service.concierge.oskellyconcierge;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import su.reddot.domain.dao.BrandRepository;
import su.reddot.domain.dao.attribute.AttributeValueRepository;
import su.reddot.domain.dao.category.CategoryRepository;
import su.reddot.domain.dao.product.ProductModelRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.attribute.AttributeValue;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductModel;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.product.ProductTagCategory;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.concierge.model.CurrencyDTO;
import su.reddot.domain.service.concierge.model.CustomerInfoDTO;
import su.reddot.domain.service.concierge.model.DescriptionStructureEnum;
import su.reddot.domain.service.concierge.model.ImageDTO;
import su.reddot.domain.service.concierge.model.OfferDTO;
import su.reddot.domain.service.concierge.model.ProductPlatformDTO;
import su.reddot.domain.service.concierge.model.Roles;
import su.reddot.domain.service.concierge.model.SalesInfoDTO;
import su.reddot.domain.service.concierge.model.SellerInfoDTO;
import su.reddot.domain.service.concierge.model.ShipmentRequestDTO;
import su.reddot.domain.service.concierge.model.ShopperInfoDTO;
import su.reddot.domain.service.concierge.model.SourcerInfoDTO;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.ProductImageDTO;
import su.reddot.domain.service.dto.ProductTagDTO;
import su.reddot.domain.service.dto.SizeValueDTO;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.staticresource.StaticResourceBalancer;
import su.reddot.domain.service.user.UserService;
import su.reddot.domain.service.user.userban.interfaces.UserBanService;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class DefaultEnrichmentService implements EnrichmentService {
    private final UserService userService;
    private final UserBanService userBanService;
    private final EmployeeConciergeRoleConverter employeeRoleConverter;
    private final StaticResourceBalancer staticResourceBalancer;
    private final BrandRepository brandRepository;
    private final CategoryRepository categoryRepository;
    private final AttributeValueRepository attributeValueRepository;
    private final ProductModelRepository productModelRepository;
    private final ProductRepository productRepository;
    private final ProductService productService;

    @Override
    public CustomerInfoDTO getCustomerInfo(CustomerInfoDTO customerInfoDTO) {
        if (customerInfoDTO == null) {
            return null;
        }

        if (customerInfoDTO.getCustomerId() == null) {
            return customerInfoDTO;
        }

        userService.getUserById(customerInfoDTO.getCustomerId()).ifPresent(user -> {
            // Заполняем только пустые поля
            if (customerInfoDTO.getCustomerNickName() == null || customerInfoDTO.getCustomerNickName().isEmpty()) {
                customerInfoDTO.setCustomerNickName(user.getNickname());
            }

            if (customerInfoDTO.getCustomerPhone() == null || customerInfoDTO.getCustomerPhone().isEmpty()) {
                customerInfoDTO.setCustomerPhone(user.getPhone());
            }

            if (customerInfoDTO.getCustomerEmail() == null || customerInfoDTO.getCustomerEmail().isEmpty()) {
                customerInfoDTO.setCustomerEmail(user.getEmail());
            }

            if (customerInfoDTO.getHasActiveBans() == null) {
                customerInfoDTO.setHasActiveBans(userBanService.hasActiveBan(customerInfoDTO.getCustomerId()));
            }
            if (customerInfoDTO.getUrlAvatar() == null || customerInfoDTO.getUrlAvatar().isEmpty()) {
                customerInfoDTO.setUrlAvatar(getFullUrlAvatar(user));
            }

        });

        return customerInfoDTO;
    }

    @Override
    public CustomerInfoDTO getCustomerInfo(Long customerId) {
        CustomerInfoDTO customerInfoDTO = new CustomerInfoDTO();
        customerInfoDTO.setCustomerId(customerId);
        return getCustomerInfo(customerInfoDTO);
    }

    @Override
    public SalesInfoDTO getSalesInfo(SalesInfoDTO salesInfoDTO) {
        if (salesInfoDTO == null) {
            return null;
        }

        if (salesInfoDTO.getSalesId() == null) {
            return salesInfoDTO;
        }

        userService.getUserById(salesInfoDTO.getSalesId()).ifPresent(user -> {
            salesInfoDTO.setFio(user.getFullName().orElse("-"));
            salesInfoDTO.setNickName(user.getNickname());
            salesInfoDTO.setUrlAvatar(getFullUrlAvatar(user));
            salesInfoDTO.setSalesRole(employeeRoleConverter.convert(Roles.class, user));
        });

        return salesInfoDTO;
    }

    @Override
    public SalesInfoDTO getSalesInfo(Long salesId) {
        SalesInfoDTO salesInfoDTO = new SalesInfoDTO();
        salesInfoDTO.setSalesId(salesId);
        return getSalesInfo(salesInfoDTO);
    }

    @Override
    public SourcerInfoDTO getSourcerInfo(long sourcerId) {
        SourcerInfoDTO sourcerInfoDTO = new SourcerInfoDTO();
        sourcerInfoDTO.setSourcerId(sourcerId);
        return getSourcerInfo(sourcerInfoDTO);
    }

    @Override
    public SourcerInfoDTO getSourcerInfo(SourcerInfoDTO sourcerInfoDTO) {
        if (sourcerInfoDTO == null) {
            return null;
        }

        if (sourcerInfoDTO.getSourcerId() == null) {
            return sourcerInfoDTO;
        }

        userService.getUserById(sourcerInfoDTO.getSourcerId()).ifPresent(user -> {
            sourcerInfoDTO.setFio(user.getFullName().orElse("-"));
            sourcerInfoDTO.setNickName(user.getNickname());
            sourcerInfoDTO.setUrlAvatar(getFullUrlAvatar(user));
        });

        return sourcerInfoDTO;
    }

    @Override
    public void fillShipmentsRequest(List<ShipmentRequestDTO> shipments, Long authorizedUserId) {
        // === Map ID -> Наименование бренда ===
        Map<Long, String> brandNameMap = loadAttributeMap(
                shipments,
                ShipmentRequestDTO::getBrandId,
                brandRepository::findAllById,
                Brand::getId,
                Brand::getName
        );

        // === Map ID -> Наименование бренда на русском ===
        Map<Long, String> brandTransliterateNameMap = loadAttributeMap(
                shipments,
                ShipmentRequestDTO::getBrandId,
                brandRepository::findAllById,
                Brand::getId,
                Brand::getTransliterateName
        );

        // === Map ID -> Наименование категории ===
        Map<Long, String> categoryNameMap = loadAttributeMap(
                shipments,
                ShipmentRequestDTO::getCategoryId,
                categoryRepository::findAllById,
                Category::getId,
                Category::getDisplayName
        );

        // === Map ID -> Наименование цвета ===
        Map<Long, String> colorAttributeNameMap = loadAttributeMap(
                shipments,
                ShipmentRequestDTO::getColorAttributeId,
                attributeValueRepository::findAllById,
                AttributeValue::getId,
                AttributeValue::getValue
        );

        // === Map ID -> Наименование материала ===
        Map<Long, String> materialAttributeNameMap = loadAttributeMap(
                shipments,
                ShipmentRequestDTO::getMaterialAttributeId,
                attributeValueRepository::findAllById,
                AttributeValue::getId,
                AttributeValue::getValue
        );

        // === Map ID -> Наименование модели ===
        Map<Long, String> modelNameMap = loadAttributeMap(
                shipments,
                ShipmentRequestDTO::getModelId,
                productModelRepository::findAllById,
                ProductModel::getId,
                ProductModel::getName
        );

        shipments.forEach(shipment -> shipment
                .creatorId(authorizedUserId)
                .brandName(brandNameMap.get(shipment.getBrandId()))
                .brandTransliterateName(brandTransliterateNameMap.get(shipment.getBrandId()))
                .categoryName(categoryNameMap.get(shipment.getCategoryId()))
                .colorAttributeName(colorAttributeNameMap.get(shipment.getColorAttributeId()))
                .modelName(modelNameMap.get(shipment.getModelId()))
                .materialAttributeName(materialAttributeNameMap.get(shipment.getMaterialAttributeId()))
        );
    }

    @Override
    public void fillShipmentRequest(ShipmentRequestDTO shipment, Long authorizedUserId) {
        List<ShipmentRequestDTO> shipments = Collections.singletonList(shipment);
        fillShipmentsRequest(shipments, authorizedUserId);
    }

    @Override
    public SellerInfoDTO getSellerInfo(SellerInfoDTO sellerInfoDTO) {
        if (sellerInfoDTO == null) {
            return null;
        }
        if (sellerInfoDTO.getSellerId() == null) {
            return null;
        }

        userService.getUserById(sellerInfoDTO.getSellerId()).ifPresent(user -> {
            // Заполняем только пустые поля
            if (sellerInfoDTO.getSellerFio() == null || sellerInfoDTO.getSellerFio().isEmpty()) {
                sellerInfoDTO.setSellerFio(user.getFullName().orElse(""));
            }

            if (sellerInfoDTO.getSellerNickname() == null || sellerInfoDTO.getSellerNickname().isEmpty()) {
                sellerInfoDTO.setSellerNickname(user.getNickname());
            }

            if (sellerInfoDTO.getSellerEmail() == null || sellerInfoDTO.getSellerEmail().isEmpty()) {
                sellerInfoDTO.setSellerEmail(user.getEmail());
            }

            if (sellerInfoDTO.getUrlAvatar() == null || sellerInfoDTO.getUrlAvatar().isEmpty()) {
                sellerInfoDTO.setUrlAvatar(getFullUrlAvatar(user));
            }
            if (sellerInfoDTO.getSellerType() == null) {
                sellerInfoDTO.setSellerType(new DescriptionStructureEnum()
                        .code(user.getSellerType().name())
                        .localizedDescription(user.getSellerType().getDescription()));
            }
        });
        return sellerInfoDTO;
    }

    private String getFullUrlAvatar(User user) {
        return staticResourceBalancer.getImageFullPath(user.getAvatarPath());
    }

    /**
     * Загрузка Map наименований атрибутов товаров
     *
     * @param shipments         список товаров
     * @param idExtractor       метод извлечения ID атрибута товара
     * @param loader            метод репозитория для получения сущностей по ID
     * @param entityIdExtractor метод получения ID сущности
     * @param valueExtractor    метод получения ID наименования
     * @param <ENTITY>          сущность атрибута товара (бренд, модель и др.)
     * @param <ID>              Тип идентификатора сущности
     * @param <VALUE>           Тип наименования сущности
     * @return Map ID и наименования атрибутов товаров
     */
    private <ENTITY, ID, VALUE> Map<ID, VALUE> loadAttributeMap(
            List<ShipmentRequestDTO> shipments,
            Function<ShipmentRequestDTO, ID> idExtractor,
            Function<List<ID>, List<ENTITY>> loader,
            Function<ENTITY, ID> entityIdExtractor,
            Function<ENTITY, VALUE> valueExtractor
    ) {
        List<ID> ids = shipments.stream()
                .map(idExtractor)
                .distinct()
                .collect(Collectors.toList());

        return loader.apply(ids)
                .stream()
                .collect(Collectors.toMap(entityIdExtractor, valueExtractor));
    }

    @Override
    public ProductPlatformDTO enrichProduct(OfferDTO offer, Product product) {
        ProductPlatformDTO offerProduct = offer.getProduct();
        if (offerProduct == null) {
            return null;
        }
        if (offerProduct.getProductId() == null) {
            return null;
        }
        ProductDTO productDTO = productService.getProductDTO(product.getId(), ProductService.UserType.SYSTEM);
        setBasicProductInfo(offerProduct, productDTO);
        setPricingInfo(offerProduct, productDTO);
        setAvailableSizes(offerProduct, productDTO);
        return offerProduct;
    }

    @Override
    public ShopperInfoDTO getShopperInfo(ShopperInfoDTO shopper) {
        if (shopper == null || shopper.getUserId() == null) {
            return null;
        }
        ShopperInfoDTO shopperInfoDTO = new ShopperInfoDTO();
        userService.getUserById(shopper.getUserId()).ifPresent(user -> {
            shopperInfoDTO.setId(shopper.getId());
            shopperInfoDTO.setUserId(shopper.getUserId());
            shopperInfoDTO.setFio(shopper.getFio() != null ? shopper.getFio() : user.getFullName().orElse("-"));
            shopperInfoDTO.setNickName(shopper.getNickName() != null ? shopper.getNickName() : user.getNickname());
            shopperInfoDTO.setUrlAvatar(getFullUrlAvatar(user));
            shopperInfoDTO.setPaymentFormat(shopper.getPaymentFormat() != null ? shopper.getPaymentFormat() : null);
            shopperInfoDTO.setInteractionTypes(shopper.getInteractionTypes() != null ? shopper.getInteractionTypes() : null);
        });
        return shopperInfoDTO;
    }

    @Override
    public void enrichProductInfo(ProductPlatformDTO productPlatformDTO) {
        if (productPlatformDTO == null || productPlatformDTO.getProductId() == null) {
            return;
        }

        ProductDTO productDTO = productService.getProductDTO(productPlatformDTO.getProductId(), ProductService.UserType.SYSTEM);
        productRepository.findById(productPlatformDTO.getProductId())
                .ifPresent(product -> enrichProductWithData(productPlatformDTO, productDTO));
    }

    private void setBasicProductInfo(ProductPlatformDTO dto, ProductDTO product) {
        dto.setCurrencyPrice(product.getPrice());
        dto.setProductId(product.getProductId());
        setBrandIfPresent(dto, product);
        enrichLocation(dto, product);
    }

    private void setBrandIfPresent(ProductPlatformDTO dto, ProductDTO product) {
        if (product.getBrand() != null) {
            dto.setBrand(product.getBrand().getName());
        }
    }

    private void setPricingInfo(ProductPlatformDTO dto, ProductDTO product) {
        BigDecimal priceWithoutCommission = product.getPriceWithoutCommission();
        BigDecimal currentPrice = product.getPrice();
        if (priceWithoutCommission != null) {
            dto.setPriceWithoutDiscount(priceWithoutCommission);
        }
        if (currentPrice != null) {
            dto.setPriceWithDiscount(currentPrice);
        }
        calculateAndSetDiscount(dto, priceWithoutCommission, currentPrice);
    }

    private void calculateAndSetDiscount(ProductPlatformDTO dto, BigDecimal priceWithoutCommission, BigDecimal currentPrice) {
        if (priceWithoutCommission != null && currentPrice != null) {
            dto.setDiscountAmount(priceWithoutCommission.subtract(currentPrice));
        }
    }

    private void setAvailableSizes(ProductPlatformDTO dto, ProductDTO product) {
        if (product.getSizes() == null || product.getSizes().isEmpty()) {
            return;
        }
        List<String> availableSizes = product.getSizes().stream()
                .map(SizeValueDTO::getProductSizeValue)
                .filter(size -> size != null && !size.trim().isEmpty())
                .distinct()
                .collect(Collectors.toList());
        dto.setAvailableSizes(availableSizes);
    }

    private void enrichProductWithData(ProductPlatformDTO dto, ProductDTO product) {
        enrichPricing(dto, product);
        enrichBrandInfo(dto, product);
        enrichCategoryInfo(dto, product);
        enrichMainImage(dto, product);
        enrichSizes(dto, product);
        enrichLocation(dto, product);
        enrichSizeType(dto, product);
        enrichConditionInfo(dto, product);
        enrichProductState(dto, product);
        enrichLikesInfo(dto, product);
        enrichUrl(dto, product);
        enrichDiscountPercent(dto, product);
        enrichCurrency(dto, product);
        enrichIsSoldInfo(dto, product);
    }

    // Используем ProductDTO для получения человекочитаемого наименования категории
    private void enrichCategoryInfo(ProductPlatformDTO dto, ProductDTO product) {
        if (dto.getProductCategory() == null && product.getCategory() != null && product.getCategory().getDisplayName() != null) {
            dto.setProductCategory(product.getCategory().getDisplayName());
        }
    }

    private void enrichPricing(ProductPlatformDTO dto, ProductDTO product) {
        if (dto.getCurrencyPrice() == null) {
            dto.setCurrencyPrice(product.getPrice());
        }
        if (dto.getPriceWithoutDiscount() == null) {
            dto.setPriceWithoutDiscount(
                    product.getRrpPrice() != null ? product.getRrpPrice() : product.getPrice()
            );
        }
        if (dto.getPriceWithDiscount() == null) {
            dto.setPriceWithDiscount(product.getPrice());
        }
        calculateDiscountIfNeeded(dto, product);
    }

    private void calculateDiscountIfNeeded(ProductPlatformDTO dto, ProductDTO product) {
        if (dto.getDiscountAmount() == null &&
                product.getRrpPrice() != null &&
                product.getPrice() != null) {
            BigDecimal discountAmount = product.getRrpPrice().subtract(product.getPrice());
            if (discountAmount.compareTo(BigDecimal.ZERO) > 0) {
                dto.setDiscountAmount(discountAmount);
            }
        }
    }

    private void enrichBrandInfo(ProductPlatformDTO dto, ProductDTO product) {
        if (dto.getBrand() == null && product.getBrand() != null) {
            dto.setBrand(product.getBrand().getName());
        }
    }

    private void enrichMainImage(ProductPlatformDTO dto, ProductDTO product) {
        if (product.getImages() != null && !product.getImages().isEmpty()) {
            ProductImageDTO img = product.getImages().get(0);
            ImageDTO imageDTO = new ImageDTO();
            imageDTO.setUrl(staticResourceBalancer.getImageFullPath(img.getPath()));
            dto.setProductPhoto(imageDTO);
        }
    }

    private void enrichSizes(ProductPlatformDTO dto, ProductDTO product) {
        if (dto.getAvailableSizes() != null && !dto.getAvailableSizes().isEmpty()) {
            return;
        }
        if (product.getSizes() != null && !product.getSizes().isEmpty()) {
            List<String> availableSizes = product.getSizes().stream()
                    .map(SizeValueDTO::getProductSizeValue)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            if (!availableSizes.isEmpty()) {
                dto.setAvailableSizes(availableSizes);
            }
        }
    }

    private void enrichLocation(ProductPlatformDTO dto, ProductDTO product) {
        if (product.getTags() != null && !product.getTags().isEmpty()) {
            dto.setProductLocation(product.getTags().stream()
                    .filter(tag -> tag.getCategory() == ProductTagCategory.LOCATION)
                    .map(ProductTagDTO::getShortName)
                    .collect(Collectors.joining(", ")));
        }
    }

    private void enrichSizeType(ProductPlatformDTO dto, ProductDTO product) {
        if (dto.getSizeType() == null && product.getSizeType() != null) {
            dto.setSizeType(product.getSizeType().name());
        }
    }

    private void enrichConditionInfo(ProductPlatformDTO dto, ProductDTO product) {
        if (product.getConditionId() != null) {
            if (dto.getConditionId() == null) {
                dto.setConditionId(product.getConditionId().intValue());
            }
            if (dto.getConditionName() == null && product.getConditionName() != null) {
                dto.setConditionName(product.getConditionName());
            }
        }
    }

    private void enrichProductState(ProductPlatformDTO dto, ProductDTO product) {
        if (dto.getProductState() == null && product.getProductState() != null) {
            dto.setProductState(product.getProductState().name());
        }
    }

    private void enrichLikesInfo(ProductPlatformDTO dto, ProductDTO product) {
        if (dto.getLikesCount() == null && product.getLikesCount() != null) {
            dto.setLikesCount(product.getLikesCount());
        }
    }

    private void enrichUrl(ProductPlatformDTO dto, ProductDTO product) {
        if (dto.getUrl() == null && product.getUrl() != null) {
            dto.setUrl(product.getUrl());
        }
    }

    private void enrichDiscountPercent(ProductPlatformDTO dto, ProductDTO product) {
        if (dto.getDiscount() == null && product.getPrice() != null && product.getRrpPrice() != null) {
            BigDecimal currentPrice = product.getPrice();
            BigDecimal rrpPrice = product.getRrpPrice();
            if (rrpPrice.compareTo(BigDecimal.ZERO) > 0 && currentPrice.compareTo(rrpPrice) < 0) {
                BigDecimal discountAmount = rrpPrice.subtract(currentPrice);
                BigDecimal discountPercent = discountAmount.divide(rrpPrice, 4, BigDecimal.ROUND_HALF_UP)
                        .multiply(BigDecimal.valueOf(100));
                dto.setDiscount(discountPercent);
            }
        }
    }

    private void enrichCurrency(ProductPlatformDTO dto, ProductDTO product) {
        if (dto.getCurrency() == null && product.getCurrency() != null) {
            CurrencyDTO conciergeCurrency = new CurrencyDTO();
            conciergeCurrency.setId(product.getCurrency().getId());
            conciergeCurrency.setIsoCode(product.getCurrency().getIsoCode());
            conciergeCurrency.setName(product.getCurrency().getName());
            conciergeCurrency.setSign(product.getCurrency().getSign());
            dto.setCurrency(conciergeCurrency);
        }
    }

    private void enrichIsSoldInfo(ProductPlatformDTO dto, ProductDTO product) {
        // Будем помечать как проданный для нашего контекста в любом состоянии кроме PUBLISHED
        dto.setIsSold(!ProductState.PUBLISHED.equals(product.getProductState()));
    }
}
