package su.reddot.domain.service.concierge.oskellyconcierge;

import su.reddot.domain.model.product.Product;
import su.reddot.domain.service.concierge.model.CustomerInfoDTO;
import su.reddot.domain.service.concierge.model.OfferDTO;
import su.reddot.domain.service.concierge.model.ProductPlatformDTO;
import su.reddot.domain.service.concierge.model.SalesInfoDTO;
import su.reddot.domain.service.concierge.model.SellerInfoDTO;
import su.reddot.domain.service.concierge.model.ShipmentRequestDTO;
import su.reddot.domain.service.concierge.model.ShopperInfoDTO;
import su.reddot.domain.service.concierge.model.SourcerInfoDTO;

import java.util.List;

/**
 * Сервис обогащения заявок
 */
public interface EnrichmentService {
    /**
     * Получить информацию о клиенте
     * @return - информация о клиенте
     */
    CustomerInfoDTO getCustomerInfo(CustomerInfoDTO customerInfoDTO);
    /**
     * Получить информацию о клиенте
     * @return - информация о клиенте
     */
    CustomerInfoDTO getCustomerInfo(Long customerId);

    /**
     * Получить информацию о продавце
     * @return - информация о продавце
     */
    SalesInfoDTO getSalesInfo(SalesInfoDTO salesInfoDTO);
    /**
     * Получить информацию о продавце
     * @return - информация о продавце
     */
    SalesInfoDTO getSalesInfo(Long salesId);
    /**
     * Получить информацию о сорсере
     * @param sourcerId - ID сорсера
     * @return - информация о сорсере
     */
    SourcerInfoDTO getSourcerInfo(long sourcerId);

    /**
     * Получить информацию о сорсере
     * @return - информация о сорсере
     */
    SourcerInfoDTO getSourcerInfo(SourcerInfoDTO sourcerInfoDTO);

    /**
     * Наполнить данные о товарах наименованиями их атрибутов
     * @param shipments товары
     * @param authorizedUserId авторизованный пользователь
     */
    void fillShipmentsRequest(List<ShipmentRequestDTO> shipments, Long authorizedUserId);

    /**
     * Наполнить данные о товаре наименованиями его атрибутов
     * @param shipment товар
     * @param authorizedUserId авторизованный пользователь
     */
    void fillShipmentRequest(ShipmentRequestDTO shipment, Long authorizedUserId);

    /**
     * Получить информацию о продавце
     * @param sellerInfoDTO - информация о продавце
     * @return - информация о продавце
     */
    SellerInfoDTO getSellerInfo(SellerInfoDTO sellerInfoDTO);

    /**
     * Получить информацию о покупателе
     * @param shopper - описание покупателя
     * @return - информация о покупателе
     */
    ShopperInfoDTO getShopperInfo(ShopperInfoDTO shopper);

    /**
     * Обогащает ProductPlatformDTO данными из Product
     * @param offer предложение с продуктом для обогащения
     * @param product продукт с данными для обогащения
     * @return обогащенный ProductPlatformDTO
     */
    ProductPlatformDTO enrichProduct(OfferDTO offer, Product product);

    /**
     * Обогащает информацию о продукте дополнительными данными
     * @param productPlatformDTO продукт для обогащения
     */
    void enrichProductInfo(ProductPlatformDTO productPlatformDTO);
}
